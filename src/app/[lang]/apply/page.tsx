import JobApplicationForm from "@/components/JobApplicationForm";

export default async function JobApplicationPage({ 
  params, 
  searchParams 
}: { 
  params: Promise<{ lang: string }>; 
  searchParams: Promise<{ job?: string }> 
}) {
  const { lang } = await params;
  const { job } = await searchParams;
  const validLang = ['en', 'sv'].includes(lang) ? lang : 'en';
  
  // Default job title if none provided
  const jobTitle = job || 'General Application';

  return (
    <main className="flex-grow bg-gradient-to-br from-purple-900 via-purple-800 to-purple-900 min-h-screen">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="text-sm text-purple-300 mb-2 uppercase tracking-wide">
            SOFTWARE DEVELOPMENT AND ENGINEERING • BITOLA, SKOPJE • HYBRID
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {jobTitle}
          </h1>
        </div>

        {/* Application Form */}
        <div className="max-w-2xl mx-auto">
          <JobApplicationForm jobTitle={jobTitle} />
        </div>
      </div>
    </main>
  );
}
