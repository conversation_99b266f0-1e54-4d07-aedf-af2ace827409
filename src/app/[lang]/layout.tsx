import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

export default async function LangLayout({ children, params }: Readonly<{ children: React.ReactNode; params: Promise<{ lang: string }> }>) {
  const { lang } = await params;
  const validLang = ['en', 'sv'].includes(lang) ? lang : 'en';
  return (
    <>
      <Header lang={validLang} />
      <div className="flex-grow flex flex-col">
        {children}
      </div>
      <Footer lang={validLang} />
    </>
  );
}
