import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    
    const firstName = formData.get('firstName') as string;
    const lastName = formData.get('lastName') as string;
    const email = formData.get('email') as string;
    const phone = formData.get('phone') as string;
    const jobTitle = formData.get('jobTitle') as string;
    const cv = formData.get('cv') as File;
    const additionalFiles = formData.getAll('additionalFiles') as File[];

    // Basic validation
    if (!firstName || !lastName || !email || !jobTitle) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email address' },
        { status: 400 }
      );
    }

    // CV validation
    if (!cv || cv.size === 0) {
      return NextResponse.json(
        { error: 'CV file is required' },
        { status: 400 }
      );
    }

    // File size validation (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (cv.size > maxSize) {
      return NextResponse.json(
        { error: 'CV file size must be less than 10MB' },
        { status: 400 }
      );
    }

    // Validate additional files
    for (const file of additionalFiles) {
      if (file.size > maxSize) {
        return NextResponse.json(
          { error: 'Additional files must be less than 10MB each' },
          { status: 400 }
        );
      }
    }

    // In a real application, you would:
    // 1. Save files to cloud storage (AWS S3, Google Cloud Storage, etc.)
    // 2. Send email with <NAME_EMAIL>
    // 3. Store application data in database
    
    // For now, we'll simulate the process and log the data
    console.log('Job application submission:', {
      firstName,
      lastName,
      email,
      phone,
      jobTitle,
      cvFileName: cv.name,
      cvSize: cv.size,
      additionalFilesCount: additionalFiles.length,
      timestamp: new Date().toISOString(),
    });

    // Simulate email <NAME_EMAIL>
    const emailData = {
      to: '<EMAIL>',
      subject: `New Job Application: ${jobTitle} - ${firstName} ${lastName}`,
      html: `
        <h2>New Job Application</h2>
        <p><strong>Position:</strong> ${jobTitle}</p>
        <p><strong>Name:</strong> ${firstName} ${lastName}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Phone:</strong> ${phone || 'Not provided'}</p>
        <p><strong>CV:</strong> ${cv.name} (${(cv.size / 1024 / 1024).toFixed(2)} MB)</p>
        ${additionalFiles.length > 0 ? `
          <p><strong>Additional Files:</strong></p>
          <ul>
            ${additionalFiles.map(file => `<li>${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)</li>`).join('')}
          </ul>
        ` : ''}
        <hr>
        <p><small>Submitted at: ${new Date().toLocaleString()}</small></p>
      `,
      // In a real implementation, you would attach the files here
      attachments: [
        {
          filename: cv.name,
          content: cv, // This would be the actual file buffer
        },
        ...additionalFiles.map(file => ({
          filename: file.name,
          content: file,
        }))
      ]
    };

    // TODO: Replace this with actual email sending logic
    // Example with a hypothetical email service:
    // await emailService.sendWithAttachments(emailData);

    return NextResponse.json(
      { message: 'Application submitted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error processing job application:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
