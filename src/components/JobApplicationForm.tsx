'use client';

import { useState } from 'react';

interface JobApplicationFormProps {
  jobTitle: string;
}

interface JobApplicationFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  cv: File | null;
  additionalFiles: File[];
}

export default function JobApplicationForm({ jobTitle }: JobApplicationFormProps) {
  const [formData, setFormData] = useState<JobApplicationFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    cv: null,
    additionalFiles: [],
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCVUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({
      ...prev,
      cv: file,
    }));
  };

  const handleAdditionalFilesUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setFormData(prev => ({
      ...prev,
      additionalFiles: files,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      const submitFormData = new FormData();
      submitFormData.append('firstName', formData.firstName);
      submitFormData.append('lastName', formData.lastName);
      submitFormData.append('email', formData.email);
      submitFormData.append('phone', formData.phone);
      submitFormData.append('jobTitle', jobTitle);
      
      if (formData.cv) {
        submitFormData.append('cv', formData.cv);
      }
      
      formData.additionalFiles.forEach(file => {
        submitFormData.append('additionalFiles', file);
      });

      const response = await fetch('/api/job-application', {
        method: 'POST',
        body: submitFormData,
      });

      const result = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          cv: null,
          additionalFiles: [],
        });
        // Reset file inputs
        const cvInput = document.getElementById('cv') as HTMLInputElement;
        const additionalInput = document.getElementById('additionalFiles') as HTMLInputElement;
        if (cvInput) cvInput.value = '';
        if (additionalInput) additionalInput.value = '';
      } else {
        setSubmitStatus('error');
        setErrorMessage(result.error || 'An error occurred while submitting your application.');
      }
    } catch (error) {
      setSubmitStatus('error');
      setErrorMessage('Network error. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg p-8 shadow-xl">
      {submitStatus === 'success' && (
        <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
          <p className="font-medium">Application submitted successfully!</p>
          <p className="text-sm">We'll review your application and get back to you soon.</p>
        </div>
      )}

      {submitStatus === 'error' && (
        <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
          <p className="font-medium">Error submitting application</p>
          <p className="text-sm">{errorMessage}</p>
        </div>
      )}

      {/* Location Checkboxes */}
      <div className="mb-8">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Locations *
        </label>
        <div className="flex gap-4">
          <label className="flex items-center">
            <input type="checkbox" defaultChecked className="mr-2" />
            <span className="text-sm">Bitola</span>
          </label>
          <label className="flex items-center">
            <input type="checkbox" defaultChecked className="mr-2" />
            <span className="text-sm">Skopje</span>
          </label>
        </div>
      </div>

      <div className="border-t border-gray-200 pt-8 mb-8">
        <h3 className="text-lg font-semibold text-center mb-6">Personal information</h3>
        
        {/* LinkedIn Apply Button */}
        <div className="mb-6">
          <button
            type="button"
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors flex items-center justify-center"
          >
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
            </svg>
            Apply with LinkedIn
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
              First name *
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-[#3982a3] focus:border-[#3982a3]"
              placeholder="Jane"
            />
          </div>
          <div>
            <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
              Last name *
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-[#3982a3] focus:border-[#3982a3]"
              placeholder="Doe"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-[#3982a3] focus:border-[#3982a3]"
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
              Phone *
            </label>
            <div className="flex">
              <select className="px-3 py-3 border border-r-0 border-gray-300 rounded-l-md bg-gray-50">
                <option>🇸🇪</option>
              </select>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                required
                className="flex-1 px-4 py-3 border border-gray-300 rounded-r-md focus:ring-[#3982a3] focus:border-[#3982a3]"
                placeholder="+389 72 345 678"
              />
            </div>
          </div>
        </div>

        <div>
          <label htmlFor="cv" className="block text-sm font-medium text-gray-700 mb-2">
            Upload CV *
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center hover:border-gray-400 transition-colors">
            <input
              type="file"
              id="cv"
              onChange={handleCVUpload}
              accept=".pdf,.doc,.docx"
              required
              className="hidden"
            />
            <label htmlFor="cv" className="cursor-pointer">
              <div className="text-gray-600">
                <p className="mb-2">Drop your file or <span className="text-blue-600 underline">upload</span></p>
                <p className="text-sm text-gray-500">PDF, DOC, DOCX (max 10MB)</p>
              </div>
            </label>
            {formData.cv && (
              <p className="mt-2 text-sm text-green-600">Selected: {formData.cv.name}</p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="additionalFiles" className="block text-sm font-medium text-gray-700 mb-2">
            Additional files
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center hover:border-gray-400 transition-colors">
            <input
              type="file"
              id="additionalFiles"
              onChange={handleAdditionalFilesUpload}
              multiple
              className="hidden"
            />
            <label htmlFor="additionalFiles" className="cursor-pointer">
              <div className="text-gray-600">
                <p className="mb-2">Drop your file or <span className="text-blue-600 underline">upload</span></p>
                <p className="text-sm text-gray-500">Any format (max 10MB each)</p>
              </div>
            </label>
            {formData.additionalFiles.length > 0 && (
              <div className="mt-2 text-sm text-green-600">
                Selected: {formData.additionalFiles.map(f => f.name).join(', ')}
              </div>
            )}
          </div>
        </div>

        <div className="pt-6">
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-gray-800 text-white py-3 px-6 rounded-md font-medium hover:bg-gray-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Submitting application...' : 'Submit application'}
          </button>
        </div>

        <div className="text-xs text-gray-500 text-center pt-4">
          By submitting this application, I agree that I have read the{' '}
          <a href="#" className="text-blue-600 underline">Privacy Policy</a> and{' '}
          confirm that Nion store my personal details to be able to process my job application.
        </div>
      </form>
    </div>
  );
}
